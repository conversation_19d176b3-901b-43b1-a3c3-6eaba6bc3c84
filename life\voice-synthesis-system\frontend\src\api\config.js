/**
 * API配置文件
 * 统一管理API相关的配置信息
 */

// API基础配置
export const API_CONFIG = {
  // 基础URL
  BASE_URL: process.env.NODE_ENV === 'production' 
    ? 'https://your-production-domain.com' 
    : 'http://localhost:8080',
  
  // 请求超时时间
  TIMEOUT: 10000,
  
  // 重试次数
  RETRY_COUNT: 3,
  
  // 重试延迟时间（毫秒）
  RETRY_DELAY: 1000
}

// API端点配置
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    USER_INFO: '/user/info'
  },
  
  // 语音合成相关
  VOICE: {
    MODELS: '/api/voice/models',
    SYNTHESIS: '/voice/synthesis',
    RECORDS: '/user/synthesis-records',
    DELETE_RECORD: '/user/synthesis-records',
    BATCH_DELETE: '/user/synthesis-records/batch'
  },
  
  // 用户相关
  USER: {
    PROFILE: '/user/profile',
    PASSWORD: '/user/password',
    STATS: '/user/stats',
    POINTS: '/user/points',
    POINT_RECORDS: '/user/point-records',
    RECHARGE: '/user/points/recharge',
    AVATAR: '/user/avatar'
  }
}

// HTTP状态码
export const HTTP_STATUS = {
  SUCCESS: 200,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500
}

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  TIMEOUT_ERROR: '请求超时，请稍后重试',
  SERVER_ERROR: '服务器错误，请稍后重试',
  UNAUTHORIZED: '登录已过期，请重新登录',
  FORBIDDEN: '没有权限访问该资源',
  NOT_FOUND: '请求的资源不存在'
}
