import Vue from 'vue'
import App from './App'
import router from './router'
import store from './store'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import './styles/themes.css'
import axios from 'axios'
import Cookies from 'js-cookie'

Vue.config.productionTip = false

// 使用Element UI
Vue.use(ElementUI)

// 配置axios
axios.defaults.baseURL = 'http://localhost:8080'
axios.defaults.timeout = 10000

// 请求拦截器
axios.interceptors.request.use(
  config => {
    const token = Cookies.get('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
axios.interceptors.response.use(
  response => {
    const res = response.data
    if (res.code !== 200) {
      ElementUI.Message.error(res.message || '请求失败')
      if (res.code === 401) {
        Cookies.remove('token')
        Cookies.remove('user')
        router.push('/login')
      }
      return Promise.reject(new Error(res.message || '请求失败'))
    }
    return res
  },
  error => {
    ElementUI.Message.error(error.message || '网络错误')
    return Promise.reject(error)
  }
)

Vue.prototype.$http = axios

// 初始化主题
const theme = localStorage.getItem('theme') || 'dark'
document.documentElement.className = `theme-${theme}`

new Vue({
  el: '#app',
  router,
  store,
  components: { App },
  template: '<App/>'
})
