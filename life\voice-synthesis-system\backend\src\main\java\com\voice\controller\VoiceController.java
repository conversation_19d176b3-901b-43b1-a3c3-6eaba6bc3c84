package com.voice.controller;

import com.voice.common.Result;
import com.voice.dto.SynthesisRequest;
import com.voice.service.VoiceSynthesisService;
import com.voice.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Map;

/**
 * 语音合成控制器
 */
@RestController
@RequestMapping("/voice")
public class VoiceController {

    @Autowired
    private VoiceSynthesisService voiceSynthesisService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 语音合成
     */
    @PostMapping("/synthesis")
    public Result<Map<String, Object>> synthesizeVoice(@Valid @RequestBody SynthesisRequest request,
                                                       HttpServletRequest httpRequest) {
        try {
            // 临时修改：如果没有token，使用默认用户ID 1 进行测试
            Long userId;
            try {
                userId = getUserIdFromToken(httpRequest);
            } catch (Exception e) {
                // 如果获取token失败，使用默认用户ID 1
                userId = 1L;
            }
            Map<String, Object> result = voiceSynthesisService.synthesizeVoice(userId, request);
            return Result.success("语音合成成功", result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取可用模型列表
     */
    @GetMapping("/models")
    public Result<Map<String, Object>> getModels() {
        try {
            Map<String, Object> result = voiceSynthesisService.getAvailableModels();
            return Result.success("获取成功", result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 从请求中获取用户ID
     */
    private Long getUserIdFromToken(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
            return jwtUtil.getUserIdFromToken(token);
        }
        throw new RuntimeException("未找到有效的token");
    }
}
