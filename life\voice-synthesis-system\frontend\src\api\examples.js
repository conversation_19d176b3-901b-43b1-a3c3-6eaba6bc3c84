/**
 * API使用示例
 * 展示如何在Vue组件中使用新的API管理系统
 */

// ===== 示例1: 在Vue组件中使用统一API =====
/*
export default {
  name: 'LoginComponent',
  data() {
    return {
      loginForm: {
        username: '',
        password: ''
      },
      loading: false
    }
  },
  methods: {
    async handleLogin() {
      this.loading = true
      try {
        // 使用统一API进行登录
        const response = await this.$api.auth.login(this.loginForm)
        
        // 处理登录成功
        this.$store.dispatch('login', response.data)
        this.$message.success('登录成功')
        this.$router.push('/dashboard')
        
      } catch (error) {
        // 错误处理已在拦截器中统一处理
        console.error('登录失败:', error)
      } finally {
        this.loading = false
      }
    }
  }
}
*/

// ===== 示例2: 语音合成功能 =====
/*
export default {
  name: 'SynthesisComponent',
  data() {
    return {
      models: [],
      synthesisForm: {
        text: '',
        modelName: '',
        mediaType: 'wav'
      }
    }
  },
  methods: {
    async loadModels() {
      try {
        const response = await this.$api.voice.getModels()
        this.models = response.data.models || []
      } catch (error) {
        console.error('加载模型失败:', error)
      }
    },
    
    async handleSynthesis() {
      try {
        const response = await this.$api.voice.synthesize(this.synthesisForm)
        this.audioUrl = response.data.audioUrl
        this.$message.success('合成成功')
      } catch (error) {
        console.error('合成失败:', error)
      }
    }
  },
  
  mounted() {
    this.loadModels()
  }
}
*/

// ===== 示例3: 用户资料管理 =====
/*
export default {
  name: 'ProfileComponent',
  data() {
    return {
      userForm: {
        nickname: '',
        email: '',
        phone: ''
      },
      pointRecords: []
    }
  },
  methods: {
    async updateProfile() {
      try {
        await this.$api.user.updateProfile(this.userForm)
        this.$message.success('更新成功')
      } catch (error) {
        console.error('更新失败:', error)
      }
    },
    
    async loadPointRecords() {
      try {
        const response = await this.$api.user.getPointRecords({
          page: 1,
          size: 10
        })
        this.pointRecords = response.data.records || []
      } catch (error) {
        console.error('加载积分记录失败:', error)
      }
    }
  }
}
*/

// ===== 示例4: 按需导入API模块 =====
/*
import { auth, voice, user } from '@/api/api'

export default {
  methods: {
    async someMethod() {
      // 直接使用导入的模块
      const loginResponse = await auth.login(loginData)
      const models = await voice.getModels()
      const userStats = await user.getStats()
    }
  }
}
*/

// ===== 示例5: 使用工具函数 =====
/*
import { downloadBlob, formatDateTime, retry } from '@/api/utils'

export default {
  methods: {
    async downloadAudio(audioUrl) {
      try {
        // 使用重试机制下载音频
        const response = await retry(
          () => this.$api.voice.downloadAudio(audioUrl),
          3, // 重试3次
          1000 // 延迟1秒
        )
        
        // 下载文件
        const filename = `audio_${formatDateTime(new Date(), 'YYYYMMDD_HHmmss')}.wav`
        downloadBlob(response.data, filename)
        
      } catch (error) {
        console.error('下载失败:', error)
      }
    }
  }
}
*/

// ===== 示例6: 错误处理最佳实践 =====
/*
export default {
  methods: {
    async handleApiCall() {
      try {
        const response = await this.$api.voice.synthesize(data)
        // 处理成功响应
        return response.data
        
      } catch (error) {
        // 具体的错误处理
        if (error.response?.status === 401) {
          // 未授权，跳转登录（拦截器已处理）
          return
        }
        
        if (error.response?.status === 403) {
          // 权限不足
          this.$message.error('权限不足')
          return
        }
        
        // 其他错误
        console.error('API调用失败:', error)
        throw error
      }
    }
  }
}
*/

// ===== 示例7: 文件上传 =====
/*
export default {
  methods: {
    async uploadAvatar(file) {
      const formData = new FormData()
      formData.append('avatar', file)
      
      try {
        const response = await this.$api.user.uploadAvatar(formData)
        this.userInfo.avatar = response.data.avatarUrl
        this.$message.success('头像上传成功')
      } catch (error) {
        console.error('头像上传失败:', error)
      }
    }
  }
}
*/

// ===== 示例8: 分页数据加载 =====
/*
export default {
  data() {
    return {
      records: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      loading: false
    }
  },
  methods: {
    async loadRecords() {
      this.loading = true
      try {
        const response = await this.$api.voice.getRecords({
          page: this.pagination.current,
          size: this.pagination.pageSize
        })
        
        this.records = response.data.records || []
        this.pagination.total = response.data.total || 0
        
      } catch (error) {
        console.error('加载记录失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    handlePageChange(page) {
      this.pagination.current = page
      this.loadRecords()
    }
  }
}
*/
