/**
 * 语音合成相关API
 */
import request from './index'
import { API_ENDPOINTS } from './config'

// 获取语音模型列表
export function getVoiceModels() {
  return request({
    url: API_ENDPOINTS.VOICE.MODELS,
    method: 'get'
  })
}

// 语音合成
export function synthesizeVoice(data) {
  return request({
    url: API_ENDPOINTS.VOICE.SYNTHESIS,
    method: 'post',
    data,
    timeout: 1200000
  })
}

// 获取合成记录
export function getSynthesisRecords(params) {
  return request({
    url: API_ENDPOINTS.VOICE.RECORDS,
    method: 'get',
    params
  })
}

// 删除合成记录
export function deleteSynthesisRecord(id) {
  return request({
    url: `${API_ENDPOINTS.VOICE.DELETE_RECORD}/${id}`,
    method: 'delete'
  })
}

// 批量删除合成记录
export function batchDeleteSynthesisRecords(ids) {
  return request({
    url: API_ENDPOINTS.VOICE.BATCH_DELETE,
    method: 'delete',
    data: { ids }
  })
}

// 下载音频文件
export function downloadAudio(audioUrl) {
  return request({
    url: audioUrl,
    method: 'get',
    responseType: 'blob'
  })
}
