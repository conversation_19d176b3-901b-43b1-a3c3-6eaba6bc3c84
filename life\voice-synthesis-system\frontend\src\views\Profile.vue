<template>
  <div class="profile-container">

    <!-- 主内容区域 -->
    <div class="profile-main">
      <!-- 用户头像和基本信息 -->
      <div class="profile-header">
        <div class="avatar-section">
          <div class="user-avatar">
            <div class="avatar-ring"></div>
            <div class="avatar-content">
              <i class="el-icon-user"></i>
            </div>
          </div>
          <div class="user-basic-info">
            <h2 class="username">{{ currentUser.nickname || currentUser.username }}</h2>
            <p class="user-id">ID: {{ currentUser.id || 'USER001' }}</p>
            <div class="user-badges">
              <span class="badge active">活跃用户</span>
              <span class="badge vip" v-if="currentUser.points > 50">VIP用户</span>
            </div>
          </div>
        </div>
        <div class="quick-actions">
          <button class="action-btn edit" @click="editMode = !editMode">
            <i class="el-icon-edit"></i>
            <span>{{ editMode ? '取消编辑' : '编辑信息' }}</span>
          </button>
          <button class="action-btn password" @click="passwordDialogVisible = true">
            <i class="el-icon-key"></i>
            <span>修改密码</span>
          </button>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="profile-content">
        <!-- 个人信息卡片 -->
        <div class="info-card">
          <div class="card-glow"></div>
          <div class="card-header">
            <div class="header-icon">
              <i class="el-icon-user"></i>
            </div>
            <h3 class="card-title">个人信息</h3>
          </div>

          <div class="card-content">
            <el-form :model="userForm" :rules="rules" ref="userForm" label-width="0">
              <div class="form-grid">
                <div class="form-item">
                  <label class="form-label">用户名</label>
                  <el-input
                    v-model="userForm.username"
                    disabled
                    class="tech-input">
                  </el-input>
                </div>
                <div class="form-item">
                  <label class="form-label">昵称</label>
                  <el-input
                    v-model="userForm.nickname"
                    :disabled="!editMode"
                    class="tech-input">
                  </el-input>
                </div>
                <div class="form-item">
                  <label class="form-label">邮箱</label>
                  <el-input
                    v-model="userForm.email"
                    :disabled="!editMode"
                    class="tech-input">
                  </el-input>
                </div>
                <div class="form-item">
                  <label class="form-label">手机号</label>
                  <el-input
                    v-model="userForm.phone"
                    :disabled="!editMode"
                    class="tech-input">
                  </el-input>
                </div>
                <div class="form-item full-width">
                  <label class="form-label">注册时间</label>
                  <el-input
                    v-model="userForm.createTime"
                    disabled
                    class="tech-input">
                  </el-input>
                </div>
              </div>

              <div v-if="editMode" class="form-actions">
                <button class="save-btn" @click="saveProfile" :disabled="saving">
                  <i class="el-icon-check" v-if="!saving"></i>
                  <i class="el-icon-loading" v-if="saving"></i>
                  <span>{{ saving ? '保存中...' : '保存' }}</span>
                </button>
                <button class="cancel-btn" @click="resetForm">
                  <i class="el-icon-refresh"></i>
                  <span>重置</span>
                </button>
              </div>
            </el-form>
          </div>
        </div>

        <!-- 积分信息卡片 -->
        <div class="points-card">
          <div class="card-glow"></div>
          <div class="card-header">
            <div class="header-icon">
              <i class="el-icon-coin"></i>
            </div>
            <h3 class="card-title">积分信息</h3>
          </div>

          <div class="card-content">
            <div class="points-display">
              <div class="points-main">
                <div class="points-icon">
                  <i class="el-icon-coin"></i>
                </div>
                <div class="points-info">
                  <span class="points-number">{{ currentUser.points || 0 }}</span>
                  <span class="points-label">当前积分</span>
                </div>
              </div>
              <div class="points-ring">
                <svg class="progress-ring" width="120" height="120">
                  <circle
                    class="progress-ring-circle"
                    stroke="#409EFF"
                    stroke-width="4"
                    fill="transparent"
                    r="52"
                    cx="60"
                    cy="60"
                    :stroke-dasharray="getCircumference()"
                    :stroke-dashoffset="getStrokeDashoffset()"
                  />
                </svg>
              </div>
            </div>

            <div class="points-stats">
              <div class="stat-item">
                <div class="stat-icon">
                  <i class="el-icon-time"></i>
                </div>
                <div class="stat-content">
                  <span class="stat-number">{{ Math.floor((currentUser.points || 0) / 10) }}</span>
                  <span class="stat-label">可用次数</span>
                </div>
              </div>
              <div class="stat-item">
                <div class="stat-icon">
                  <i class="el-icon-price-tag"></i>
                </div>
                <div class="stat-content">
                  <span class="stat-number">10</span>
                  <span class="stat-label">积分/次</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 积分记录卡片 -->
        <div class="records-card">
          <div class="card-glow"></div>
          <div class="card-header">
            <div class="header-icon">
              <i class="el-icon-document"></i>
            </div>
            <h3 class="card-title">积分记录</h3>
          </div>

          <div class="card-content">
            <div v-if="pointRecords.length === 0" class="empty-state">
              <i class="el-icon-document-remove"></i>
              <p>暂无积分记录</p>
            </div>
            <div v-else class="records-list">
              <div
                v-for="record in pointRecords"
                :key="record.id || Math.random()"
                class="record-item">
                <div class="record-type">
                  <div class="type-indicator" :class="{ gain: record.type === 1, cost: record.type === 2 }">
                    <i :class="record.type === 1 ? 'el-icon-plus' : 'el-icon-minus'"></i>
                  </div>
                </div>
                <div class="record-content">
                  <div class="record-desc">{{ record.description }}</div>
                  <div class="record-time">{{ record.createTime }}</div>
                </div>
                <div class="record-points" :class="{ gain: record.type === 1, cost: record.type === 2 }">
                  {{ record.type === 1 ? '+' : '-' }}{{ record.points }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 修改密码对话框 -->
    <div v-if="passwordDialogVisible" class="password-modal" @click="closePasswordModal">
      <div class="password-modal-content" @click.stop>
        <div class="modal-header">
          <h3>修改密码</h3>
          <button class="close-btn" @click="closePasswordModal">
            <i class="el-icon-close"></i>
          </button>
        </div>
        <div class="modal-body">
          <el-form :model="passwordForm" :rules="passwordRules" ref="passwordForm" label-width="0">
            <div class="password-form">
              <div class="form-item">
                <label class="form-label">当前密码</label>
                <el-input
                  type="password"
                  v-model="passwordForm.oldPassword"
                  class="tech-input"
                  placeholder="请输入当前密码">
                </el-input>
              </div>
              <div class="form-item">
                <label class="form-label">新密码</label>
                <el-input
                  type="password"
                  v-model="passwordForm.newPassword"
                  class="tech-input"
                  placeholder="请输入新密码">
                </el-input>
              </div>
              <div class="form-item">
                <label class="form-label">确认密码</label>
                <el-input
                  type="password"
                  v-model="passwordForm.confirmPassword"
                  class="tech-input"
                  placeholder="请再次输入新密码">
                </el-input>
              </div>
            </div>
          </el-form>
        </div>
        <div class="modal-footer">
          <button class="cancel-btn" @click="closePasswordModal">取消</button>
          <button class="confirm-btn" @click="changePassword">确定</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Profile',
  data() {
    const validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }

    return {
      editMode: false,
      saving: false,
      userForm: {
        username: '',
        nickname: '',
        email: '',
        phone: '',
        createTime: ''
      },
      rules: {
        nickname: [
          { required: true, message: '请输入昵称', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      },
      pointRecords: [],
      passwordDialogVisible: false,
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        oldPassword: [
          { required: true, message: '请输入当前密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { validator: validatePass2, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['currentUser'])
  },
  methods: {
    loadUserInfo() {
      if (this.currentUser) {
        this.userForm = {
          username: this.currentUser.username,
          nickname: this.currentUser.nickname,
          email: this.currentUser.email,
          phone: this.currentUser.phone,
          createTime: this.currentUser.createTime
        }
      }
    },

    async saveProfile() {
      this.$refs.userForm.validate(async (valid) => {
        if (valid) {
          this.saving = true
          try {
            // await this.$http.put('/user/profile', this.userForm)
            this.$message.success('个人信息更新成功')
            this.editMode = false
            // 更新store中的用户信息
            // this.$store.commit('SET_USER', updatedUser)
          } catch (error) {
            console.error('更新个人信息失败:', error)
          } finally {
            this.saving = false
          }
        }
      })
    },

    resetForm() {
      this.loadUserInfo()
    },

    async loadPointRecords() {
      try {
        // const response = await this.$http.get('/user/point-records')
        // this.pointRecords = response.data.records
        
        // 模拟数据
        this.pointRecords = [
          {
            type: 1,
            points: 100,
            description: '注册赠送积分',
            createTime: '2023-12-01 10:00:00'
          },
          {
            type: 2,
            points: 10,
            description: '语音合成消耗积分',
            createTime: '2023-12-01 10:30:00'
          },
          {
            type: 2,
            points: 10,
            description: '语音合成消耗积分',
            createTime: '2023-12-01 11:00:00'
          }
        ]
      } catch (error) {
        console.error('加载积分记录失败:', error)
      }
    },

    async changePassword() {
      this.$refs.passwordForm.validate(async (valid) => {
        if (valid) {
          try {
            // await this.$http.put('/user/password', this.passwordForm)
            this.$message.success('密码修改成功')
            this.passwordDialogVisible = false
            this.passwordForm = {
              oldPassword: '',
              newPassword: '',
              confirmPassword: ''
            }
          } catch (error) {
            console.error('修改密码失败:', error)
          }
        }
      })
    },

    closePasswordModal() {
      this.passwordDialogVisible = false
      this.passwordForm = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
    },

    getCircumference() {
      return 2 * Math.PI * 52
    },

    getStrokeDashoffset() {
      const maxPoints = 200 // 假设最大积分为200
      const currentPoints = this.currentUser.points || 0
      const progress = Math.min(currentPoints / maxPoints, 1)
      const circumference = this.getCircumference()
      return circumference - (progress * circumference)
    }
  },
  mounted() {
    this.loadUserInfo()
    this.loadPointRecords()
  }
}
</script>

<style scoped>
/* 全局容器 */
.profile-container {
  position: relative;
  padding: 40px;
}

/* 主内容区域 */
.profile-main {
  position: relative;
  z-index: 10;
  max-width: 1200px;
  margin: 0 auto;
}

/* 用户头像和基本信息 */
.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  padding: 30px 40px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.avatar-section {
  display: flex;
  align-items: center;
  gap: 25px;
}

.user-avatar {
  position: relative;
  width: 80px;
  height: 80px;
}

.avatar-ring {
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border: 2px solid #409EFF;
  border-radius: 50%;
  animation: avatarPulse 2s ease-in-out infinite;
}

@keyframes avatarPulse {
  0%, 100% { transform: scale(1); opacity: 0.7; }
  50% { transform: scale(1.1); opacity: 1; }
}

.avatar-content {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: white;
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
}

.user-basic-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.username {
  font-size: 28px;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  text-shadow: 0 2px 10px rgba(64, 158, 255, 0.3);
}

.user-id {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

.user-badges {
  display: flex;
  gap: 10px;
}

.badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.badge.active {
  background: rgba(103, 194, 58, 0.2);
  color: #67C23A;
  border: 1px solid rgba(103, 194, 58, 0.3);
}

.badge.vip {
  background: rgba(255, 193, 7, 0.2);
  color: #FFC107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.quick-actions {
  display: flex;
  gap: 15px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.action-btn:hover {
  background: rgba(64, 158, 255, 0.2);
  border-color: #409EFF;
  color: #409EFF;
  transform: translateY(-2px);
}

.action-btn.password:hover {
  background: rgba(230, 162, 60, 0.2);
  border-color: #E6A23C;
  color: #E6A23C;
}

/* 主要内容区域 */
.profile-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 30px;
}

/* 通用卡片样式 */
.info-card, .points-card, .records-card {
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.info-card {
  grid-column: 1;
  grid-row: 1 / 3;
}

.points-card {
  grid-column: 2;
  grid-row: 1;
}

.records-card {
  grid-column: 2;
  grid-row: 2;
}

.info-card:hover, .points-card:hover, .records-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px rgba(64, 158, 255, 0.2);
}

.card-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(64, 158, 255, 0.1) 0%,
    rgba(103, 194, 58, 0.1) 100%);
  opacity: 0.5;
  animation: cardPulse 4s ease-in-out infinite alternate;
}

@keyframes cardPulse {
  0% { opacity: 0.3; }
  100% { opacity: 0.7; }
}

.card-header {
  position: relative;
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 25px 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 2;
}

.header-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);
}

.header-icon i {
  font-size: 18px;
  color: white;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
  text-shadow: 0 2px 10px rgba(64, 158, 255, 0.3);
}

.card-content {
  position: relative;
  padding: 30px;
  z-index: 2;
}

/* 表单样式 */
.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 25px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-item.full-width {
  grid-column: 1 / -1;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #409EFF;
}

.tech-input >>> .el-input__inner {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: #ffffff;
  height: 45px;
  padding: 0 15px;
  transition: all 0.3s ease;
}

.tech-input >>> .el-input__inner:focus {
  border-color: #409EFF;
  box-shadow: 0 0 15px rgba(64, 158, 255, 0.3);
}

.tech-input >>> .el-input__inner:disabled {
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.5);
}

.tech-input >>> .el-input__inner::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;
  padding-top: 25px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.save-btn, .cancel-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 25px;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.save-btn {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
}

.save-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(64, 158, 255, 0.4);
}

.save-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.cancel-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* 积分显示 */
.points-display {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;
}

.points-main {
  display: flex;
  align-items: center;
  gap: 20px;
}

.points-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: coinSpin 3s linear infinite;
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
}

@keyframes coinSpin {
  0% { transform: rotateY(0deg); }
  100% { transform: rotateY(360deg); }
}

.points-icon i {
  font-size: 28px;
  color: white;
}

.points-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.points-number {
  font-size: 32px;
  font-weight: 700;
  color: #409EFF;
  text-shadow: 0 2px 10px rgba(64, 158, 255, 0.5);
}

.points-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.points-ring {
  position: relative;
}

.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-circle {
  transition: stroke-dashoffset 0.5s ease-in-out;
  filter: drop-shadow(0 0 10px rgba(64, 158, 255, 0.5));
}

.points-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.stat-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-number {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
}

.stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

/* 积分记录 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.5);
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 15px;
  display: block;
}

.empty-state p {
  font-size: 16px;
  margin: 0;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-height: 300px;
  overflow-y: auto;
}

.records-list::-webkit-scrollbar {
  width: 6px;
}

.records-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.records-list::-webkit-scrollbar-thumb {
  background: rgba(64, 158, 255, 0.5);
  border-radius: 3px;
}

.record-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.record-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
}

.record-type {
  display: flex;
  align-items: center;
}

.type-indicator {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.type-indicator.gain {
  background: rgba(103, 194, 58, 0.2);
  color: #67C23A;
  border: 2px solid rgba(103, 194, 58, 0.3);
}

.type-indicator.cost {
  background: rgba(230, 162, 60, 0.2);
  color: #E6A23C;
  border: 2px solid rgba(230, 162, 60, 0.3);
}

.record-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.record-desc {
  font-size: 14px;
  color: #ffffff;
  font-weight: 500;
}

.record-time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.record-points {
  font-size: 16px;
  font-weight: 700;
}

.record-points.gain {
  color: #67C23A;
}

.record-points.cost {
  color: #E6A23C;
}

/* 密码修改模态框 */
.password-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

.password-modal-content {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 450px;
  max-width: 90vw;
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  0% { transform: translateY(-50px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
  color: #ffffff;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.close-btn {
  width: 35px;
  height: 35px;
  background: rgba(245, 108, 108, 0.2);
  border: 1px solid rgba(245, 108, 108, 0.3);
  border-radius: 50%;
  color: #F56C6C;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(245, 108, 108, 0.3);
  transform: scale(1.1);
}

.modal-body {
  padding: 30px;
}

.password-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  padding: 20px 30px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.confirm-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 25px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
}

.confirm-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(64, 158, 255, 0.4);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .profile-content {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .info-card {
    grid-column: 1;
    grid-row: 1;
  }

  .points-card {
    grid-column: 1;
    grid-row: 2;
  }

  .records-card {
    grid-column: 1;
    grid-row: 3;
  }
}

@media (max-width: 768px) {
  .profile-container {
    padding: 20px;
  }

  .profile-header {
    padding: 20px 25px;
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }

  .avatar-section {
    width: 100%;
    justify-content: center;
  }

  .quick-actions {
    width: 100%;
    justify-content: center;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .form-item.full-width {
    grid-column: 1;
  }

  .points-display {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .points-stats {
    flex-direction: column;
    gap: 15px;
  }

  .card-content {
    padding: 20px;
  }

  .card-header {
    padding: 20px 25px;
  }
}

@media (max-width: 480px) {
  .username {
    font-size: 24px;
  }

  .points-number {
    font-size: 28px;
  }

  .action-btn {
    padding: 10px 15px;
    font-size: 13px;
  }

  .password-modal-content {
    width: 95vw;
  }

  .modal-body {
    padding: 20px;
  }

  .modal-header {
    padding: 20px;
  }

  .modal-footer {
    padding: 15px 20px;
  }
}
</style>
