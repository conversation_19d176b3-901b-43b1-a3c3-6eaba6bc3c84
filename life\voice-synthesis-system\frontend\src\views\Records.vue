<template>
  <div class="records-container">

    <!-- 主内容区域 -->
    <div class="records-main">
      <div class="records-card">
        <div class="card-glow"></div>

        <!-- 头部区域 -->
        <div class="card-header">
          <div class="header-left">
            <div class="header-icon">
              <i class="el-icon-document"></i>
            </div>
            <h2 class="header-title">合成记录</h2>
          </div>
          <div class="header-actions">
            <div class="search-box">
              <el-input
                v-model="searchText"
                placeholder="搜索记录..."
                prefix-icon="el-icon-search"
                class="tech-input"
                @input="handleSearch">
              </el-input>
            </div>
            <div class="filter-box">
              <el-select
                v-model="filterStatus"
                placeholder="状态筛选"
                class="tech-select"
                @change="handleSearch">
                <el-option label="全部" value=""></el-option>
                <el-option label="成功" :value="1"></el-option>
                <el-option label="失败" :value="0"></el-option>
                <el-option label="处理中" :value="2"></el-option>
              </el-select>
            </div>
            <button class="refresh-btn" @click="loadRecords">
              <i class="el-icon-refresh"></i>
            </button>
          </div>
        </div>

        <!-- 记录列表 -->
        <div class="records-content">
          <div v-if="loading" class="loading-state">
            <div class="loading-spinner">
              <i class="el-icon-loading"></i>
            </div>
            <p>加载中...</p>
          </div>

          <div v-else-if="filteredRecords.length === 0" class="empty-state">
            <i class="el-icon-document-remove"></i>
            <p>暂无合成记录</p>
          </div>

          <div v-else class="records-grid">
            <div
              v-for="record in paginatedRecords"
              :key="record.id"
              class="record-card">
              <div class="record-header">
                <div class="record-id">#{{ record.id }}</div>
                <div class="record-status">
                  <div class="status-indicator" :class="getStatusClass(record.status)">
                    <i :class="getStatusIcon(record.status)"></i>
                  </div>
                  <span class="status-text">{{ getStatusText(record.status) }}</span>
                </div>
              </div>

              <div class="record-content">
                <div class="record-text">{{ record.text }}</div>
                <div class="record-meta">
                  <div class="meta-item">
                    <i class="el-icon-cpu"></i>
                    <span>{{ record.modelName }}</span>
                  </div>
                  <div class="meta-item">
                    <i class="el-icon-coin"></i>
                    <span>{{ record.pointsCost }}积分</span>
                  </div>
                  <div class="meta-item" v-if="record.fileSize">
                    <i class="el-icon-document"></i>
                    <span>{{ formatFileSize(record.fileSize) }}</span>
                  </div>
                  <div class="meta-item">
                    <i class="el-icon-time"></i>
                    <span>{{ record.createTime }}</span>
                  </div>
                </div>
                <div v-if="record.status === 0 && record.errorMessage" class="error-message">
                  <i class="el-icon-warning"></i>
                  <span>{{ record.errorMessage }}</span>
                </div>
              </div>

              <div class="record-actions">
                <button
                  v-if="record.audioUrl && record.status === 1"
                  class="action-btn play"
                  @click="playAudio(record.audioUrl)">
                  <i class="el-icon-video-play"></i>
                  <span>播放</span>
                </button>
                <button
                  v-if="record.audioUrl && record.status === 1"
                  class="action-btn download"
                  @click="downloadAudio(record.audioUrl)">
                  <i class="el-icon-download"></i>
                  <span>下载</span>
                </button>
                <button
                  class="action-btn delete"
                  @click="deleteRecord(record.id)">
                  <i class="el-icon-delete"></i>
                  <span>删除</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页区域 -->
        <div class="pagination-section" v-if="filteredRecords.length > 0">
          <div class="pagination-info">
            <span>共 {{ filteredRecords.length }} 条记录</span>
          </div>
          <div class="pagination-controls">
            <button
              class="page-btn"
              :disabled="currentPage === 1"
              @click="handleCurrentChange(currentPage - 1)">
              <i class="el-icon-arrow-left"></i>
            </button>
            <div class="page-numbers">
              <button
                v-for="page in getPageNumbers()"
                :key="page"
                class="page-number"
                :class="{ active: page === currentPage }"
                @click="handleCurrentChange(page)">
                {{ page }}
              </button>
            </div>
            <button
              class="page-btn"
              :disabled="currentPage === Math.ceil(filteredRecords.length / pageSize)"
              @click="handleCurrentChange(currentPage + 1)">
              <i class="el-icon-arrow-right"></i>
            </button>
          </div>
          <div class="page-size-selector">
            <el-select
              v-model="pageSize"
              @change="handleSizeChange"
              class="tech-select small">
              <el-option :value="10" label="10条/页"></el-option>
              <el-option :value="20" label="20条/页"></el-option>
              <el-option :value="50" label="50条/页"></el-option>
            </el-select>
          </div>
        </div>
      </div>
    </div>

    <!-- 音频播放对话框 -->
    <div v-if="audioDialogVisible" class="audio-modal" @click="closeAudioModal">
      <div class="audio-modal-content" @click.stop>
        <div class="modal-header">
          <h3>音频播放</h3>
          <button class="close-btn" @click="closeAudioModal">
            <i class="el-icon-close"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="audio-visualizer">
            <div class="wave-bars">
              <div class="bar" v-for="i in 15" :key="i"></div>
            </div>
          </div>
          <audio controls :src="currentAudioUrl" class="tech-audio"></audio>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Records',
  data() {
    return {
      records: [],
      loading: false,
      currentPage: 1,
      pageSize: 20,
      total: 0,
      audioDialogVisible: false,
      currentAudioUrl: '',
      searchText: '',
      filterStatus: ''
    }
  },
  computed: {
    filteredRecords() {
      let filtered = this.records

      // 文本搜索
      if (this.searchText) {
        filtered = filtered.filter(record =>
          record.text.toLowerCase().includes(this.searchText.toLowerCase()) ||
          record.modelName.toLowerCase().includes(this.searchText.toLowerCase())
        )
      }

      // 状态筛选
      if (this.filterStatus !== '') {
        filtered = filtered.filter(record => record.status === this.filterStatus)
      }

      return filtered
    },

    paginatedRecords() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredRecords.slice(start, end)
    }
  },
  methods: {
    async loadRecords() {
      this.loading = true
      try {
        // const response = await this.$http.get('/user/synthesis-records', {
        //   params: {
        //     page: this.currentPage,
        //     size: this.pageSize
        //   }
        // })
        // this.records = response.data.records
        // this.total = response.data.total
        
        // 模拟数据
        this.records = [
          {
            id: 1,
            text: '这是一个测试文本，用于语音合成',
            modelName: 'zhexue_lao',
            pointsCost: 10,
            status: 1,
            fileSize: 1024000,
            audioUrl: '/audio/test1.wav',
            createTime: '2023-12-01 10:30:00'
          },
          {
            id: 2,
            text: '另一个语音合成测试',
            modelName: 'xiaoshuo_nan',
            pointsCost: 10,
            status: 1,
            fileSize: 2048000,
            audioUrl: '/audio/test2.wav',
            createTime: '2023-12-01 09:15:00'
          },
          {
            id: 3,
            text: '失败的合成尝试',
            modelName: 'zhexue_lao',
            pointsCost: 10,
            status: 0,
            errorMessage: '模型加载失败',
            createTime: '2023-11-30 16:45:00'
          }
        ]
        this.total = 3
      } catch (error) {
        console.error('加载合成记录失败:', error)
        this.$message.error('加载合成记录失败')
      } finally {
        this.loading = false
      }
    },

    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },

    handleCurrentChange(val) {
      this.currentPage = val
    },

    handleSearch() {
      this.currentPage = 1
    },

    playAudio(audioUrl) {
      this.currentAudioUrl = `http://localhost:8080/api${audioUrl}`
      this.audioDialogVisible = true
    },

    closeAudioModal() {
      this.audioDialogVisible = false
      this.currentAudioUrl = ''
    },

    downloadAudio(audioUrl) {
      const link = document.createElement('a')
      link.href = `http://localhost:8080/api${audioUrl}`
      link.download = `synthesis_${Date.now()}.wav`
      link.click()
    },

    deleteRecord(id) {
      this.$confirm('确定要删除这条记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里应该调用删除API
        this.records = this.records.filter(record => record.id !== id)
        this.$message.success('删除成功')
      }).catch(() => {
        // 用户取消删除
      })
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    getStatusClass(status) {
      return {
        'success': status === 1,
        'error': status === 0,
        'processing': status === 2
      }
    },

    getStatusIcon(status) {
      switch (status) {
        case 1: return 'el-icon-success'
        case 0: return 'el-icon-error'
        case 2: return 'el-icon-loading'
        default: return 'el-icon-warning'
      }
    },

    getStatusText(status) {
      switch (status) {
        case 1: return '成功'
        case 0: return '失败'
        case 2: return '处理中'
        default: return '未知'
      }
    },

    getPageNumbers() {
      const totalPages = Math.ceil(this.filteredRecords.length / this.pageSize)
      const pages = []
      const maxVisible = 5

      let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2))
      let end = Math.min(totalPages, start + maxVisible - 1)

      if (end - start + 1 < maxVisible) {
        start = Math.max(1, end - maxVisible + 1)
      }

      for (let i = start; i <= end; i++) {
        pages.push(i)
      }

      return pages
    }
  },
  mounted() {
    this.loadRecords()
  }
}
</script>

<style scoped>
/* 全局容器 */
.records-container {
  position: relative;
  padding: 40px;
}

/* 主内容区域 */
.records-main {
  position: relative;
  z-index: 10;
  max-width: 1200px;
  margin: 0 auto;
}

.records-card {
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.records-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px rgba(64, 158, 255, 0.2);
}

.card-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(64, 158, 255, 0.1) 0%,
    rgba(103, 194, 58, 0.1) 100%);
  opacity: 0.5;
  animation: cardPulse 4s ease-in-out infinite alternate;
}

@keyframes cardPulse {
  0% { opacity: 0.3; }
  100% { opacity: 0.7; }
}

/* 卡片头部 */
.card-header {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px 40px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 2;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);
}

.header-icon i {
  font-size: 24px;
  color: white;
}

.header-title {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  text-shadow: 0 2px 10px rgba(64, 158, 255, 0.3);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.search-box, .filter-box {
  width: 200px;
}

.tech-input >>> .el-input__inner,
.tech-select >>> .el-input__inner {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: #ffffff;
  height: 40px;
  transition: all 0.3s ease;
}

.tech-input >>> .el-input__inner:focus,
.tech-select >>> .el-input__inner:focus {
  border-color: #409EFF;
  box-shadow: 0 0 15px rgba(64, 158, 255, 0.3);
}

.tech-input >>> .el-input__inner::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.tech-input >>> .el-input__prefix,
.tech-select >>> .el-input__suffix {
  color: rgba(255, 255, 255, 0.7);
}

.refresh-btn {
  width: 40px;
  height: 40px;
  background: rgba(64, 158, 255, 0.2);
  border: 1px solid rgba(64, 158, 255, 0.3);
  border-radius: 50%;
  color: #409EFF;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-btn:hover {
  background: rgba(64, 158, 255, 0.3);
  transform: rotate(180deg);
}

/* 记录内容区域 */
.records-content {
  position: relative;
  padding: 40px;
  z-index: 2;
}

.loading-state, .empty-state {
  text-align: center;
  padding: 80px 20px;
  color: rgba(255, 255, 255, 0.5);
}

.loading-spinner {
  font-size: 48px;
  margin-bottom: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state i {
  font-size: 64px;
  margin-bottom: 20px;
  display: block;
}

.empty-state p, .loading-state p {
  font-size: 18px;
  margin: 0;
}

/* 记录网格 */
.records-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 25px;
}

.record-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 25px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.record-card:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(64, 158, 255, 0.2);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.record-id {
  font-size: 14px;
  font-weight: 600;
  color: #409EFF;
  background: rgba(64, 158, 255, 0.2);
  padding: 4px 12px;
  border-radius: 12px;
}

.record-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.status-indicator.success {
  background: rgba(103, 194, 58, 0.2);
  color: #67C23A;
}

.status-indicator.error {
  background: rgba(245, 108, 108, 0.2);
  color: #F56C6C;
}

.status-indicator.processing {
  background: rgba(230, 162, 60, 0.2);
  color: #E6A23C;
  animation: spin 1s linear infinite;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
}

.record-content {
  margin-bottom: 20px;
}

.record-text {
  color: #ffffff;
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.record-meta {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.7);
}

.meta-item i {
  font-size: 14px;
  color: #409EFF;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 15px;
  padding: 10px 15px;
  background: rgba(245, 108, 108, 0.1);
  border: 1px solid rgba(245, 108, 108, 0.3);
  border-radius: 8px;
  color: #F56C6C;
  font-size: 13px;
}

.record-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 15px;
  border: none;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn.play {
  background: rgba(64, 158, 255, 0.2);
  color: #409EFF;
  border: 1px solid rgba(64, 158, 255, 0.3);
}

.action-btn.play:hover {
  background: rgba(64, 158, 255, 0.3);
  transform: translateY(-2px);
}

.action-btn.download {
  background: rgba(103, 194, 58, 0.2);
  color: #67C23A;
  border: 1px solid rgba(103, 194, 58, 0.3);
}

.action-btn.download:hover {
  background: rgba(103, 194, 58, 0.3);
  transform: translateY(-2px);
}

.action-btn.delete {
  background: rgba(245, 108, 108, 0.2);
  color: #F56C6C;
  border: 1px solid rgba(245, 108, 108, 0.3);
}

.action-btn.delete:hover {
  background: rgba(245, 108, 108, 0.3);
  transform: translateY(-2px);
}

/* 分页区域 */
.pagination-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 40px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 2;
}

.pagination-info {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-btn {
  width: 35px;
  height: 35px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-btn:hover:not(:disabled) {
  background: rgba(64, 158, 255, 0.2);
  border-color: #409EFF;
  color: #409EFF;
}

.page-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 5px;
}

.page-number {
  width: 35px;
  height: 35px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.page-number:hover {
  background: rgba(64, 158, 255, 0.2);
  border-color: #409EFF;
  color: #409EFF;
}

.page-number.active {
  background: #409EFF;
  border-color: #409EFF;
  color: white;
  box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);
}

.page-size-selector {
  width: 120px;
}

.tech-select.small >>> .el-input__inner {
  height: 35px;
  font-size: 13px;
}

/* 音频模态框 */
.audio-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

.audio-modal-content {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0;
  width: 500px;
  max-width: 90vw;
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  0% { transform: translateY(-50px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
  color: #ffffff;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.close-btn {
  width: 35px;
  height: 35px;
  background: rgba(245, 108, 108, 0.2);
  border: 1px solid rgba(245, 108, 108, 0.3);
  border-radius: 50%;
  color: #F56C6C;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(245, 108, 108, 0.3);
  transform: scale(1.1);
}

.modal-body {
  padding: 30px;
}

.audio-visualizer {
  display: flex;
  justify-content: center;
  margin-bottom: 25px;
}

.wave-bars {
  display: flex;
  align-items: end;
  gap: 3px;
  height: 50px;
}

.bar {
  width: 4px;
  background: linear-gradient(to top, #409EFF, #67C23A);
  border-radius: 2px;
  animation: waveAnimation 1.5s ease-in-out infinite;
}

.bar:nth-child(odd) {
  animation-delay: 0.1s;
}

.bar:nth-child(even) {
  animation-delay: 0.3s;
}

@keyframes waveAnimation {
  0%, 100% { height: 15px; }
  50% { height: 40px; }
}

.tech-audio {
  width: 100%;
  height: 50px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .records-container {
    padding: 20px;
  }

  .card-header {
    padding: 20px 25px;
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    flex-wrap: wrap;
    gap: 10px;
  }

  .search-box, .filter-box {
    flex: 1;
    min-width: 150px;
  }

  .records-content {
    padding: 25px;
  }

  .records-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .record-meta {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .pagination-section {
    padding: 20px 25px;
    flex-direction: column;
    gap: 15px;
  }

  .pagination-controls {
    order: 1;
  }

  .pagination-info {
    order: 2;
  }

  .page-size-selector {
    order: 3;
  }
}
</style>
