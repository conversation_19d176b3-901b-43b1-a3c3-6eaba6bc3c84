<template>
  <div class="home-container">
    <!-- 主内容区域 -->
    <div class="main-container">
      <!-- 欢迎区域 -->
      <div class="welcome-section">
        <div class="welcome-card">
          <div class="card-glow"></div>
          <div class="welcome-content">
            <h2 class="welcome-title">欢迎使用语音合成系统</h2>
            <div class="welcome-stats">
              <div class="stat-item">
                <span class="stat-label">您当前拥有</span>
                <span class="stat-value">{{ userPoints }}</span>
                <span class="stat-unit">积分</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">每次语音合成消耗</span>
                <span class="stat-value">10</span>
                <span class="stat-unit">积分</span>
              </div>
            </div>

            <div class="action-buttons">
              <button class="primary-btn" @click="$router.push('/layout/synthesis')">
                <i class="el-icon-microphone"></i>
                <span>开始合成</span>
                <div class="btn-glow"></div>
              </button>
              <button class="secondary-btn" @click="$router.push('/layout/records')">
                <i class="el-icon-document"></i>
                <span>查看记录</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据统计卡片 -->
      <div class="stats-section">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="card-background"></div>
            <div class="stat-icon-wrapper">
              <i class="el-icon-coin"></i>
            </div>
            <div class="stat-data">
              <h3 class="stat-number">{{ userPoints }}</h3>
              <p class="stat-description">当前积分</p>
            </div>
            <div class="stat-trend">
              <i class="el-icon-top-right trend-up"></i>
            </div>
          </div>

          <div class="stat-card">
            <div class="card-background"></div>
            <div class="stat-icon-wrapper synthesis">
              <i class="el-icon-microphone"></i>
            </div>
            <div class="stat-data">
              <h3 class="stat-number">{{ synthesisCount }}</h3>
              <p class="stat-description">合成次数</p>
            </div>
            <div class="stat-trend">
              <i class="el-icon-top-right trend-up"></i>
            </div>
          </div>

          <div class="stat-card">
            <div class="card-background"></div>
            <div class="stat-icon-wrapper available">
              <i class="el-icon-time"></i>
            </div>
            <div class="stat-data">
              <h3 class="stat-number">{{ Math.floor(userPoints / 10) }}</h3>
              <p class="stat-description">可用次数</p>
            </div>
            <div class="stat-trend">
              <i class="el-icon-top-right trend-up"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Home',
  data() {
    return {
      synthesisCount: 0
    }
  },
  computed: {
    ...mapGetters(['currentUser', 'userPoints'])
  },
  methods: {
    async loadStats() {
      try {
        // 这里可以加载用户统计信息
        // const response = await this.$http.get('/user/stats')
        // this.synthesisCount = response.data.synthesisCount
      } catch (error) {
        console.error('加载统计信息失败:', error)
      }
    }
  },
  mounted() {
    this.loadStats()
  }
}
</script>

<style scoped>
/* 全局容器 */
.home-container {
  position: relative;
}

/* 主内容区域 */
.main-container {
  padding: 40px;
  max-width: 1400px;
  margin: 0 auto;
}

/* 欢迎区域 */
.welcome-section {
  margin-bottom: 40px;
}

.welcome-card {
  position: relative;
  background: var(--bg-card);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid var(--border-primary);
  overflow: hidden;
  transition: all 0.3s ease;
}

.welcome-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(64, 158, 255, 0.2);
}

.card-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(64, 158, 255, 0.1) 0%,
    rgba(103, 194, 58, 0.1) 100%);
  opacity: 0.5;
  animation: cardPulse 4s ease-in-out infinite alternate;
}

@keyframes cardPulse {
  0% { opacity: 0.3; }
  100% { opacity: 0.7; }
}

.welcome-content {
  position: relative;
  padding: 40px;
  z-index: 2;
}

.welcome-title {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 30px 0;
  text-align: center;
  text-shadow: 0 2px 10px var(--shadow-accent);
}

.welcome-stats {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 15px 25px;
  background: var(--bg-secondary);
  border-radius: 15px;
  border: 1px solid var(--border-secondary);
}

.stat-label {
  color: var(--text-secondary);
  font-size: 14px;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-accent);
  text-shadow: 0 2px 10px var(--shadow-accent);
}

.stat-unit {
  color: var(--text-secondary);
  font-size: 14px;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.primary-btn, .secondary-btn {
  position: relative;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 30px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.primary-btn {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
}

.primary-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(64, 158, 255, 0.4);
}

.btn-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent);
  transition: left 0.5s ease;
}

.primary-btn:hover .btn-glow {
  left: 100%;
}

.secondary-btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.secondary-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* 统计卡片区域 */
.stats-section {
  margin-top: 40px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.stat-card {
  position: relative;
  background: var(--bg-card);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid var(--border-primary);
  padding: 30px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px rgba(64, 158, 255, 0.2);
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(64, 158, 255, 0.1) 0%,
    rgba(64, 158, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover .card-background {
  opacity: 1;
}

.stat-icon-wrapper {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}

.stat-icon-wrapper.synthesis {
  background: linear-gradient(135deg, #67C23A, #85CE61);
  box-shadow: 0 8px 25px rgba(103, 194, 58, 0.3);
}

.stat-icon-wrapper.available {
  background: linear-gradient(135deg, #E6A23C, #F7BA2A);
  box-shadow: 0 8px 25px rgba(230, 162, 60, 0.3);
}

.stat-card:hover .stat-icon-wrapper {
  transform: scale(1.1) rotate(5deg);
}

.stat-icon-wrapper i {
  font-size: 32px;
  color: white;
}

.stat-data {
  margin-bottom: 15px;
}

.stat-number {
  font-size: 36px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 8px 0;
  text-shadow: 0 2px 10px var(--shadow-accent);
}

.stat-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
  font-weight: 500;
}

.stat-trend {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background: rgba(103, 194, 58, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.trend-up {
  color: #67C23A;
  font-size: 18px;
  animation: trendPulse 2s ease-in-out infinite;
}

@keyframes trendPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 20px;
  }

  .nav-menu {
    display: none;
  }

  .main-container {
    padding: 20px;
  }

  .welcome-stats {
    flex-direction: column;
    gap: 15px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .system-title {
    font-size: 20px;
  }

  .welcome-title {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .user-section {
    gap: 15px;
  }

  .points-display {
    padding: 8px 15px;
  }

  .welcome-content {
    padding: 25px;
  }

  .stat-card {
    padding: 20px;
  }
}
</style>
